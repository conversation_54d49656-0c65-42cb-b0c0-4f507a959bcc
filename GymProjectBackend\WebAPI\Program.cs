using System;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Business.Abstract;
using Business.Concrete;
using Business.DependencyResolvers.Autofac;
using Core.Utilities.IoC;
using Core.Utilities.Security.Encryption;
using Core.Utilities.Security.JWT;
using Core.Utilities.Security.Environment;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Core.Extensions;
using Core.DependencyResolvers;
using Core.Extentions;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.CrossCuttingConcerns.Logging;
using System.Diagnostics;
using AspNetCoreRateLimit;

var builder = WebApplication.CreateBuilder(args);

builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory())
    .ConfigureContainer<ContainerBuilder>(builder =>
    {
        builder.RegisterModule(new AutofacBusinessModule());
    });

// Environment anahtarını oku
var environment = builder.Configuration["Environment"] ?? "dev";

// Environment'a göre CORS ayarlarını al
var allowedOrigins = builder.Configuration.GetSection($"AllowedOrigins:{environment}").Get<string[]>() ?? new[] { "http://localhost:4200" };

// CORS yapılandırması
builder.Services.AddCors(options =>
{
    if (environment == "dev")
    {
        // Development ortamında tüm origin'lere izin ver
        options.AddPolicy("AllowSpecificOrigins",
            policy =>
            {
                policy.AllowAnyOrigin()
                       .AllowAnyHeader()
                       .AllowAnyMethod();
            });
    }
    else
    {
        // Production/Staging ortamında sadece belirli origin'lere izin ver
        options.AddPolicy("AllowSpecificOrigins",
            policy =>
            {
                policy.WithOrigins(allowedOrigins)
                       .AllowAnyHeader()
                       .AllowAnyMethod()
                       .AllowCredentials();
            });
    }
});

// DbContext DI kaydı (Scalability için)
var connectionString = builder.Configuration[$"ConnectionStrings:{environment}"];
if (string.IsNullOrEmpty(connectionString))
{
    throw new InvalidOperationException($"'{environment}' environment'ı için connection string bulunamadı!");
}

// DbContext kaydı AutofacBusinessModule'de yapılıyor

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddHttpContextAccessor();
builder.Services.AddSingleton<FileLoggerService>();
builder.Services.AddSingleton<PerformanceLoggerService>();
builder.Services.AddSingleton<ILogService, FileLoggerService>();
builder.Services.AddSingleton<Stopwatch>();

// Redis Cache Services
builder.Services.AddSingleton<Core.Cache.Abstract.IRedisConnectionService, Core.Cache.Concrete.RedisConnectionService>();
builder.Services.AddSingleton<Core.Cache.Abstract.ICacheKeyGenerator, Core.Cache.Concrete.CacheKeyGenerator>();
builder.Services.AddSingleton<Core.Cache.Abstract.IRedisService, Core.Cache.Concrete.RedisService>();

// Environment'a göre TokenOptions'ı al
var tokenOptions = builder.Configuration.GetSection($"TokenOptions:{environment}").Get<TokenOptions>();

if (tokenOptions == null)
{
    throw new InvalidOperationException($"'{environment}' environment'ı için TokenOptions bulunamadı!");
}

// SecurityKey'i environment helper ile güvenli şekilde al
var secureKey = EnvironmentSecurityHelper.GetSecurityKey(environment, tokenOptions.SecurityKey);

// SecurityKey güvenlik kontrolü
if (!EnvironmentSecurityHelper.ValidateSecurityKey(secureKey))
{
    throw new InvalidOperationException($"'{environment}' environment'ı için SecurityKey güvenlik gereksinimlerini karşılamıyor!");
}

tokenOptions.SecurityKey = secureKey;

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidIssuer = tokenOptions.Issuer,
            ValidAudience = tokenOptions.Audience,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = SecurityKeyHelper.CreateSecurityKey(tokenOptions.SecurityKey)
        };

        // Token doğrulama hatalarını yakalayıp uygun HTTP yanıtları döndürme
        options.Events = new JwtBearerEvents
        {
            OnChallenge = context =>
            {
                // 401 Unauthorized yanıtı özelleştirme
                context.HandleResponse();
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";
                var result = System.Text.Json.JsonSerializer.Serialize(new { success = false, message = "Oturum süresi dolmuş veya geçersiz. Lütfen tekrar giriş yapın." });
                return context.Response.WriteAsync(result);
            },
            OnForbidden = context =>
            {
                // 403 Forbidden yanıtı özelleştirme
                context.Response.StatusCode = 403;
                context.Response.ContentType = "application/json";
                var result = System.Text.Json.JsonSerializer.Serialize(new { success = false, message = "Bu işlem için yetkiniz bulunmamaktadır." });
                return context.Response.WriteAsync(result);
            }
        };
    });

builder.Services.AddDependencyResolvers(new ICoreModule[] { new CoreModule() });

// AspNetCoreRateLimit konfigürasyonu
builder.Services.AddOptions();
builder.Services.AddMemoryCache();
builder.Services.Configure<IpRateLimitOptions>(builder.Configuration.GetSection("IpRateLimiting"));
builder.Services.Configure<IpRateLimitPolicies>(builder.Configuration.GetSection("IpRateLimitPolicies"));
builder.Services.AddInMemoryRateLimiting();
builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
var app = builder.Build();

// ServiceTool'a Autofac container'ını set et
Core.Utilities.IoC.ServiceTool.SetServiceProvider(app.Services);

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Swagger her zaman kullanılabilir
app.UseSwagger();
app.UseSwaggerUI();

app.ConfigureCustomExceptionMiddleware();

// Environment'a göre CORS politikasını uygula
app.UseCors("AllowSpecificOrigins");

// Development ortamında HTTPS redirect'i devre dışı bırak (mobil erişim için)
if (environment != "dev")
{
    app.UseHttpsRedirection();
}

// AspNetCoreRateLimit middleware'i ekleniyor
app.UseIpRateLimiting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();