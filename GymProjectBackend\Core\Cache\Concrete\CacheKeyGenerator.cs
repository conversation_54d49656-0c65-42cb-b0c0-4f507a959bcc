using Core.Cache.Abstract;
using Microsoft.Extensions.Configuration;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace Core.Cache.Concrete
{
    /// <summary>
    /// Multi-tenant cache key generation service
    /// Company-based cache isolation için key generation implementasyonu
    /// </summary>
    public class CacheKeyGenerator : ICacheKeyGenerator
    {
        private readonly string _keyPrefix;
        private readonly string _environment;

        public CacheKeyGenerator(IConfiguration configuration)
        {
            _environment = configuration["Environment"] ?? "dev";
            _keyPrefix = configuration[$"CacheSettings:{_environment}:KeyPrefix"] ?? "gymkod:dev";
        }

        public string GenerateMethodKey(int companyId, string serviceName, string methodName, object[] parameters)
        {
            var paramHash = GenerateParameterHash(parameters);
            return $"{_keyPrefix}:company:{companyId}:service:{serviceName}:method:{methodName}:params:{paramHash}";
        }

        public string GenerateEntityKey(int companyId, string entityName, int id)
        {
            return $"{_keyPrefix}:company:{companyId}:entity:{entityName}:id:{id}";
        }

        public string GenerateListKey(int companyId, string entityName, string filterHash)
        {
            return $"{_keyPrefix}:company:{companyId}:list:{entityName}:filter:{filterHash}";
        }

        public string GenerateSessionKey(int companyId, int userId, string sessionType)
        {
            return $"{_keyPrefix}:company:{companyId}:session:{sessionType}:user:{userId}";
        }

        public string GenerateSettingsKey(int companyId, string settingKey)
        {
            return $"{_keyPrefix}:company:{companyId}:settings:{settingKey}";
        }

        public string GenerateRateLimitKey(int companyId, string identifier, string action)
        {
            return $"{_keyPrefix}:company:{companyId}:ratelimit:{action}:{identifier}";
        }

        public string GenerateCompanyPattern(int companyId, string pattern)
        {
            return $"{_keyPrefix}:company:{companyId}:{pattern}";
        }

        public string GenerateMethodKey(int companyId, MethodInfo methodInfo, object[] parameters)
        {
            var serviceName = methodInfo.DeclaringType?.Name ?? "Unknown";
            var methodName = methodInfo.Name;
            return GenerateMethodKey(companyId, serviceName, methodName, parameters);
        }

        public string GenerateParameterHash(object[] parameters)
        {
            if (parameters == null || parameters.Length == 0)
                return "empty";

            try
            {
                // Parametreleri JSON'a serialize et
                var parameterData = new List<object>();
                foreach (var param in parameters)
                {
                    if (param == null)
                    {
                        parameterData.Add("null");
                    }
                    else if (param.GetType().IsPrimitive || param is string || param is DateTime || param is decimal)
                    {
                        parameterData.Add(param);
                    }
                    else
                    {
                        // Complex object'leri JSON'a serialize et
                        parameterData.Add(JsonSerializer.Serialize(param));
                    }
                }

                var json = JsonSerializer.Serialize(parameterData);
                
                // SHA256 hash oluştur
                using var sha256 = SHA256.Create();
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(json));
                
                // Base64 encode et ve URL-safe yap
                var hash = Convert.ToBase64String(hashBytes)
                    .Replace('+', '-')
                    .Replace('/', '_')
                    .Replace("=", "");

                // İlk 16 karakteri al (collision riski düşük, key uzunluğu makul)
                return hash.Substring(0, Math.Min(16, hash.Length));
            }
            catch (Exception)
            {
                // Fallback: parametrelerin string representation'ının hash'i
                var fallbackString = string.Join("|", parameters.Select(p => p?.ToString() ?? "null"));
                using var sha256 = SHA256.Create();
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(fallbackString));
                var hash = Convert.ToBase64String(hashBytes)
                    .Replace('+', '-')
                    .Replace('/', '_')
                    .Replace("=", "");
                return hash.Substring(0, Math.Min(16, hash.Length));
            }
        }
    }
}
